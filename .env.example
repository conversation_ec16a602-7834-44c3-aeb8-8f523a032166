# 环境配置
NODE_ENV=development

# 服务端口配置
FRONTEND_PORT=3000
BACKEND_PORT=3001

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ai-digital-robots
MONGODB_USERNAME=admin
MONGODB_PASSWORD=password123

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=redis123

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production-minimum-32-characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# AI模型API配置
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_ORG_ID=your-openai-org-id-here
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here

# 向量数据库配置 (Pinecone)
PINECONE_API_KEY=your-pinecone-api-key-here
PINECONE_ENVIRONMENT=your-pinecone-environment-here
PINECONE_INDEX_NAME=ai-digital-robots

# 向量数据库配置 (Weaviate) - 可选
WEAVIATE_URL=http://localhost:8080
WEAVIATE_API_KEY=your-weaviate-api-key-here

# 文件存储配置 (MinIO/S3)
MINIO_ENDPOINT=localhost
MINIO_PORT=9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=ai-digital-robots
MINIO_USE_SSL=false

# AWS S3配置 (生产环境推荐)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=ai-digital-robots-prod

# 语音服务配置 (Azure Speech Services)
AZURE_SPEECH_KEY=your-azure-speech-key-here
AZURE_SPEECH_REGION=eastus

# 阿里云语音服务配置 (可选)
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
ALIYUN_SPEECH_REGION=cn-shanghai

# 邮件服务配置 (用于用户注册验证等)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# 监控和日志配置
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn-here

# 限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AI_WINDOW_MS=60000
RATE_LIMIT_AI_MAX_REQUESTS=20

# 文件上传配置
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,docx,doc,txt,md,rtf

# 安全配置
CORS_ORIGIN=http://localhost:3000,https://yourdomain.com
COOKIE_SECRET=your-cookie-secret-here
ENCRYPTION_KEY=your-32-character-encryption-key-here

# 第三方服务配置
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# 支付配置 (如果需要)
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# 分析和统计
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# 开发环境特定配置
DEBUG=ai-digital-robots:*
MOCK_AI_RESPONSES=false
ENABLE_API_DOCS=true
