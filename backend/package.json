{"name": "ai-digital-robots-backend", "version": "1.0.0", "description": "AI数字人定制应用系统 - 后端API服务", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit", "db:seed": "ts-node src/scripts/seed.ts", "db:migrate": "ts-node src/scripts/migrate.ts"}, "keywords": ["ai", "digital-human", "chatbot", "rag", "llm", "nodejs", "express", "mongodb", "typescript"], "author": "AI Digital Robots Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "axios": "^1.6.2", "openai": "^4.20.1", "@anthropic-ai/sdk": "^0.9.1", "langchain": "^0.0.208", "@pinecone-database/pinecone": "^1.1.2", "pdf-parse": "^1.1.1", "mammoth": "^1.6.0", "node-cron": "^3.0.3", "compression": "^1.7.4", "express-slow-down": "^2.0.1", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.14", "hpp": "^0.2.3", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/hpp": "^0.2.5", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts", "!src/scripts/**"]}}