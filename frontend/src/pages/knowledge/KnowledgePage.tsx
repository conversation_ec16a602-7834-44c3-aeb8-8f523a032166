import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import { UploadOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

export const KnowledgePage: React.FC = () => {
  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>知识库</Title>
          <Text type="secondary">管理您的文档和知识库</Text>
        </div>
        <Button type="primary" icon={<UploadOutlined />}>
          上传文档
        </Button>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">知识库功能正在开发中...</Text>
        </div>
      </Card>
    </div>
  )
}
