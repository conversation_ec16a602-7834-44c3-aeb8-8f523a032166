{"name": "ai-digital-robots-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "AI数字人定制应用系统 - 前端应用", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "dayjs": "^1.11.10", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "framer-motion": "^10.16.16", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-dropzone": "^14.2.3", "react-audio-player": "^0.17.0", "wavesurfer.js": "^7.6.0", "recharts": "^2.8.0", "ahooks": "^3.7.8"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "typescript": "^5.2.2", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "playwright": "^1.40.0", "@playwright/test": "^1.40.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/node": "^20.9.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}