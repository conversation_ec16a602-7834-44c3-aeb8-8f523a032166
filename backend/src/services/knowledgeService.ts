import path from 'path'
import fs from 'fs/promises'
import { Knowledge, KnowledgeDocument } from '@/models/Knowledge'
import { User } from '@/models/User'
import { ragService } from '@/services/rag/ragService'
import { logger } from '@/utils/logger'
import { AppError } from '@/middleware/errorHandler'
import { PaginationParams, FileUploadInfo } from '@/types'
import { RAGConfig } from '@/services/rag/types'

/**
 * 知识库服务类
 */
export class KnowledgeService {
  /**
   * 创建知识库
   */
  static async createKnowledge(
    userId: string,
    knowledgeData: {
      name: string
      description?: string
      settings?: {
        chunkSize?: number
        chunkOverlap?: number
        embeddingModel?: string
      }
    }
  ): Promise<KnowledgeDocument> {
    try {
      // 验证用户是否存在
      const user = await User.findById(userId)
      if (!user) {
        throw new AppError('用户不存在', 404, 'USER_NOT_FOUND')
      }

      // 创建知识库
      const knowledge = new Knowledge({
        name: knowledgeData.name,
        description: knowledgeData.description || '',
        userId,
        documents: [],
        settings: {
          chunkSize: knowledgeData.settings?.chunkSize || 1000,
          chunkOverlap: knowledgeData.settings?.chunkOverlap || 200,
          embeddingModel: knowledgeData.settings?.embeddingModel || 'text-embedding-ada-002',
        },
      })

      await knowledge.save()

      // 创建向量存储索引
      const indexName = `knowledge_${knowledge._id}`
      const ragConfig: RAGConfig = {
        chunkSize: knowledge.settings.chunkSize,
        chunkOverlap: knowledge.settings.chunkOverlap,
        embeddingModel: knowledge.settings.embeddingModel,
        vectorStore: {
          provider: 'pinecone',
          indexName,
          dimension: 1536, // OpenAI embedding dimension
        },
        retrieval: {
          topK: 5,
          scoreThreshold: 0.7,
          rerankEnabled: false,
        },
      }

      // 初始化向量存储
      knowledge.vectorStore = {
        provider: 'pinecone',
        indexId: indexName,
        dimensions: 1536,
        totalVectors: 0,
      }

      await knowledge.save()

      logger.info('知识库创建成功', {
        knowledgeId: knowledge._id,
        userId,
        name: knowledge.name,
      })

      return knowledge
    } catch (error) {
      logger.error('知识库创建失败:', error)
      throw error
    }
  }

  /**
   * 上传文档到知识库
   */
  static async uploadDocument(
    knowledgeId: string,
    userId: string,
    fileInfo: FileUploadInfo
  ): Promise<any> {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权访问该知识库', 403, 'ACCESS_DENIED')
      }

      // 检查文件类型
      const supportedTypes = ragService.getSupportedDocumentTypes()
      const fileExtension = path.extname(fileInfo.originalName).slice(1).toLowerCase()
      
      if (!supportedTypes.includes(fileExtension)) {
        throw new AppError(
          `不支持的文件类型: ${fileExtension}`,
          400,
          'UNSUPPORTED_FILE_TYPE'
        )
      }

      // 添加文档记录
      const documentData = {
        name: fileInfo.originalName,
        originalName: fileInfo.originalName,
        type: fileExtension as any,
        size: fileInfo.size,
        url: fileInfo.path,
        status: 'processing' as const,
        chunks: 0,
        uploadedAt: new Date(),
      }

      await knowledge.addDocument(documentData)
      const document = knowledge.documents[knowledge.documents.length - 1]

      // 异步处理文档
      this.processDocumentAsync(knowledge, document._id.toString(), fileInfo.path)

      logger.info('文档上传成功', {
        knowledgeId,
        documentId: document._id,
        fileName: fileInfo.originalName,
        userId,
      })

      return document
    } catch (error) {
      logger.error('文档上传失败:', error)
      throw error
    }
  }

  /**
   * 异步处理文档
   */
  private static async processDocumentAsync(
    knowledge: KnowledgeDocument,
    documentId: string,
    filePath: string
  ): Promise<void> {
    try {
      const ragConfig: RAGConfig = {
        chunkSize: knowledge.settings.chunkSize,
        chunkOverlap: knowledge.settings.chunkOverlap,
        embeddingModel: knowledge.settings.embeddingModel,
        vectorStore: {
          provider: knowledge.vectorStore?.provider || 'pinecone',
          indexName: knowledge.vectorStore?.indexId || `knowledge_${knowledge._id}`,
          dimension: knowledge.vectorStore?.dimensions || 1536,
        },
        retrieval: {
          topK: 5,
          scoreThreshold: 0.7,
          rerankEnabled: false,
        },
      }

      const document = knowledge.getDocumentById(documentId)
      if (!document) {
        throw new Error('文档不存在')
      }

      // 处理文档
      const result = await ragService.uploadDocument(
        filePath,
        document.originalName,
        documentId,
        ragConfig
      )

      // 更新文档状态
      if (result.status === 'success') {
        await knowledge.updateDocumentStatus(documentId, 'completed', result.chunks)
        logger.info('文档处理完成', {
          knowledgeId: knowledge._id,
          documentId,
          chunks: result.chunks,
          tokens: result.tokens,
        })
      } else {
        await knowledge.updateDocumentStatus(documentId, 'failed')
        logger.error('文档处理失败', {
          knowledgeId: knowledge._id,
          documentId,
          error: result.error,
        })
      }

      // 清理临时文件
      try {
        await fs.unlink(filePath)
      } catch (error) {
        logger.warn('清理临时文件失败:', error)
      }
    } catch (error) {
      logger.error('文档异步处理失败:', error)
      
      try {
        await knowledge.updateDocumentStatus(documentId, 'failed')
      } catch (updateError) {
        logger.error('更新文档状态失败:', updateError)
      }
    }
  }

  /**
   * 删除文档
   */
  static async deleteDocument(
    knowledgeId: string,
    documentId: string,
    userId: string
  ): Promise<void> {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权访问该知识库', 403, 'ACCESS_DENIED')
      }

      const document = knowledge.getDocumentById(documentId)
      if (!document) {
        throw new AppError('文档不存在', 404, 'DOCUMENT_NOT_FOUND')
      }

      // 从向量存储中删除
      if (document.status === 'completed' && knowledge.vectorStore) {
        const ragConfig: RAGConfig = {
          chunkSize: knowledge.settings.chunkSize,
          chunkOverlap: knowledge.settings.chunkOverlap,
          embeddingModel: knowledge.settings.embeddingModel,
          vectorStore: {
            provider: knowledge.vectorStore.provider,
            indexName: knowledge.vectorStore.indexId,
            dimension: knowledge.vectorStore.dimensions,
          },
          retrieval: {
            topK: 5,
            scoreThreshold: 0.7,
            rerankEnabled: false,
          },
        }

        await ragService.deleteDocument(documentId, ragConfig)
      }

      // 从知识库中移除文档
      await knowledge.removeDocument(documentId)

      // 删除文件
      try {
        await fs.unlink(document.url)
      } catch (error) {
        logger.warn('删除文件失败:', error)
      }

      logger.info('文档删除成功', {
        knowledgeId,
        documentId,
        fileName: document.name,
        userId,
      })
    } catch (error) {
      logger.error('文档删除失败:', error)
      throw error
    }
  }

  /**
   * 搜索知识库
   */
  static async searchKnowledge(
    knowledgeId: string,
    userId: string,
    query: string,
    options: {
      topK?: number
      scoreThreshold?: number
    } = {}
  ) {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权访问该知识库', 403, 'ACCESS_DENIED')
      }

      if (!knowledge.vectorStore) {
        throw new AppError('知识库未初始化', 400, 'KNOWLEDGE_NOT_INITIALIZED')
      }

      const ragConfig: RAGConfig = {
        chunkSize: knowledge.settings.chunkSize,
        chunkOverlap: knowledge.settings.chunkOverlap,
        embeddingModel: knowledge.settings.embeddingModel,
        vectorStore: {
          provider: knowledge.vectorStore.provider,
          indexName: knowledge.vectorStore.indexId,
          dimension: knowledge.vectorStore.dimensions,
        },
        retrieval: {
          topK: options.topK || 5,
          scoreThreshold: options.scoreThreshold || 0.7,
          rerankEnabled: false,
        },
      }

      const result = await ragService.searchDocuments(query, ragConfig, {
        topK: options.topK,
        scoreThreshold: options.scoreThreshold,
      })

      logger.info('知识库搜索完成', {
        knowledgeId,
        query,
        resultsCount: result.chunks.length,
        userId,
      })

      return result
    } catch (error) {
      logger.error('知识库搜索失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库列表
   */
  static async getKnowledgeList(
    userId: string,
    params: PaginationParams = {}
  ) {
    const {
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      search,
    } = params

    const query: any = { userId }

    // 搜索功能
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
      ]
    }

    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1

    const [knowledgeList, total] = await Promise.all([
      Knowledge.find(query)
        .sort(sortOptions)
        .skip((page - 1) * limit)
        .limit(limit),
      Knowledge.countDocuments(query),
    ])

    return {
      knowledgeList,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    }
  }

  /**
   * 获取知识库详情
   */
  static async getKnowledge(knowledgeId: string, userId: string): Promise<KnowledgeDocument> {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权访问该知识库', 403, 'ACCESS_DENIED')
      }

      return knowledge
    } catch (error) {
      logger.error('获取知识库详情失败:', error)
      throw error
    }
  }

  /**
   * 更新知识库
   */
  static async updateKnowledge(
    knowledgeId: string,
    userId: string,
    updates: {
      name?: string
      description?: string
      settings?: {
        chunkSize?: number
        chunkOverlap?: number
        embeddingModel?: string
      }
    }
  ): Promise<KnowledgeDocument> {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权修改该知识库', 403, 'ACCESS_DENIED')
      }

      // 更新字段
      if (updates.name) knowledge.name = updates.name
      if (updates.description !== undefined) knowledge.description = updates.description
      if (updates.settings) {
        if (updates.settings.chunkSize) knowledge.settings.chunkSize = updates.settings.chunkSize
        if (updates.settings.chunkOverlap) knowledge.settings.chunkOverlap = updates.settings.chunkOverlap
        if (updates.settings.embeddingModel) knowledge.settings.embeddingModel = updates.settings.embeddingModel
      }

      await knowledge.save()

      logger.info('知识库更新成功', {
        knowledgeId,
        userId,
        name: knowledge.name,
      })

      return knowledge
    } catch (error) {
      logger.error('知识库更新失败:', error)
      throw error
    }
  }

  /**
   * 删除知识库
   */
  static async deleteKnowledge(knowledgeId: string, userId: string): Promise<void> {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权删除该知识库', 403, 'ACCESS_DENIED')
      }

      // TODO: 检查是否有数字人在使用该知识库

      // 删除所有文档文件
      for (const document of knowledge.documents) {
        try {
          await fs.unlink(document.url)
        } catch (error) {
          logger.warn('删除文档文件失败:', error)
        }
      }

      // 删除向量存储索引
      if (knowledge.vectorStore) {
        // TODO: 实现向量存储索引删除
      }

      // 删除知识库
      await Knowledge.findByIdAndDelete(knowledgeId)

      logger.info('知识库删除成功', {
        knowledgeId,
        userId,
        name: knowledge.name,
      })
    } catch (error) {
      logger.error('知识库删除失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库统计信息
   */
  static async getKnowledgeStats(knowledgeId: string, userId: string) {
    try {
      const knowledge = await Knowledge.findById(knowledgeId)
      if (!knowledge) {
        throw new AppError('知识库不存在', 404, 'KNOWLEDGE_NOT_FOUND')
      }

      // 验证权限
      if (knowledge.userId.toString() !== userId) {
        throw new AppError('无权访问该知识库', 403, 'ACCESS_DENIED')
      }

      // 获取向量存储统计
      let vectorStats = null
      if (knowledge.vectorStore) {
        try {
          const ragConfig: RAGConfig = {
            chunkSize: knowledge.settings.chunkSize,
            chunkOverlap: knowledge.settings.chunkOverlap,
            embeddingModel: knowledge.settings.embeddingModel,
            vectorStore: {
              provider: knowledge.vectorStore.provider,
              indexName: knowledge.vectorStore.indexId,
              dimension: knowledge.vectorStore.dimensions,
            },
            retrieval: {
              topK: 5,
              scoreThreshold: 0.7,
              rerankEnabled: false,
            },
          }

          vectorStats = await ragService.getIndexStats(ragConfig)
        } catch (error) {
          logger.warn('获取向量存储统计失败:', error)
        }
      }

      return {
        totalDocuments: knowledge.documentCount,
        completedDocuments: knowledge.completedDocumentCount,
        processingDocuments: knowledge.processingDocumentCount,
        failedDocuments: knowledge.failedDocumentCount,
        totalSize: knowledge.totalSize,
        totalChunks: knowledge.totalChunks,
        vectorStats,
        isReady: knowledge.isReady,
      }
    } catch (error) {
      logger.error('获取知识库统计失败:', error)
      throw error
    }
  }
}
