import { Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

export const CharactersPage: React.FC = () => {
  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2}>数字人管理</Title>
          <Text type="secondary">创建和管理您的AI数字人角色</Text>
        </div>
        <Button type="primary" icon={<PlusOutlined />}>
          创建数字人
        </Button>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">数字人管理功能正在开发中...</Text>
        </div>
      </Card>
    </div>
  )
}
